<template>
  <q-dialog v-model:model-value="isShown" class="dialog-positions">
    <q-card class="positions-main">
      <!-- Header -->
      <div class="positions-header">
        <div class="text-h6">Your Positions</div>
        <q-btn icon="close" size="sm" flat @click="onClickClose" />
      </div>

      <!-- Loading state -->
      <div v-if="isLoading" class="q-pa-md text-center">
        <q-spinner size="lg" />
        <div class="q-mt-sm">Loading positions...</div>
      </div>

      <!-- No positions state -->
      <div v-else-if="groupedPositions.length === 0" class="q-pa-md text-center text-grey-6">
        No positions found
      </div>

      <!-- Positions content -->
      <div v-else class="positions-content">
        <!-- Column headers -->
        <div class="positions-header-row">
          <div class="header-icon"></div>
          <div class="header-market" @click="onClickSort('title')" :class="getSortClass('title')">
            Market
            <q-icon :name="getSortIcon('title')" size="xs" />
          </div>
          <div class="header-outcome" @click="onClickSort('outcome')" :class="getSortClass('outcome')">
            Outcome
          </div>
          <div class="header-price" @click="onClickSort('price')" :class="getSortClass('price')">
            Price
          </div>
          <div class="header-shares" @click="onClickSort('shares')" :class="getSortClass('shares')">
            Shares
          </div>
          <div class="header-cost" @click="onClickSort('cost')" :class="getSortClass('cost')">
            Cost
          </div>
          <div class="header-value" @click="onClickSort('value')" :class="getSortClass('value')">
            Value
          </div>
          <div class="header-pnl" @click="onClickSort('pnl')" :class="getSortClass('pnl')">
            P&L
          </div>
        </div>

        <!-- Event groups -->
        <div v-for="eventGroup in groupedPositions" :key="eventGroup.eventSlug" class="event-group">
          <!-- Event header with icon and title -->
          <div class="event-header">
            <div class="event-icon">
              <img :src="eventGroup.icon" :alt="eventGroup.title" />
            </div>
            <div class="event-title">
              {{ eventGroup.title }}
            </div>
          </div>

          <!-- Position rows for this event -->
          <div v-for="position in eventGroup.positions" :key="position.asset" class="position-row">
            <div class="row-spacer"></div>
            <div class="row-market">{{ position.title }}</div>
            <div class="row-outcome" :class="getOutcomeClass(position.outcome)">
              {{ position.outcome }}
            </div>
            <div class="row-price">{{ formatCents(position.curPrice) }}</div>
            <div class="row-shares">{{ formatDecimal(position.size, 2, true) }}</div>
            <div class="row-cost">{{ formatCurrency(position.initialValue, 0) }}</div>
            <div class="row-value">{{ formatCurrency(position.currentValue, 0) }}</div>
            <div class="row-pnl" :class="getPnlClass(position.cashPnl)">
              {{ formatCurrency(position.cashPnl, 0) }}
              <span class="pnl-percent">({{ formatDecimal(position.percentPnl, 1, true) }}%)</span>
            </div>
          </div>
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { PolyApiPosition, PolyPositionSort } from "@shared/api-dataclasses-shared";
import { formatCurrency, formatCents, formatDecimal } from "src/utils";
import { useUserStore } from "src/stores/user-store";
import { api } from "src/api";

const props = defineProps<{
}>();

const isShown = defineModel("isShown", { type: Boolean, default: false });

interface EventGroup {
  eventSlug: string;
  title: string;
  icon: string;
  positions: PolyApiPosition[];
}

const user = useUserStore();
const isLoading = ref(false);
const positions = ref<PolyApiPosition[]>([]);
const sortBy = ref<string>("title");
const sortDesc = ref(false);

const groupedPositions = computed(() => {
  if (!positions.value.length) return [];

  //Group positions by event
  const groups: Record<string, EventGroup> = {};

  for (const position of positions.value) {
    if (!groups[position.eventSlug]) {
      groups[position.eventSlug] = {
        eventSlug: position.eventSlug,
        title: position.title,
        icon: position.icon,
        positions: []
      };
    }
    groups[position.eventSlug].positions.push(position);
  }

  //Convert to array and sort each group's positions
  const groupArray = Object.values(groups);

  for (const group of groupArray) {
    group.positions.sort((a, b) => {
      let aVal: any, bVal: any;

      switch (sortBy.value) {
        case "title":
          aVal = a.title.toLowerCase();
          bVal = b.title.toLowerCase();
          break;
        case "outcome":
          aVal = a.outcome.toLowerCase();
          bVal = b.outcome.toLowerCase();
          break;
        case "price":
          aVal = a.curPrice;
          bVal = b.curPrice;
          break;
        case "shares":
          aVal = a.size;
          bVal = b.size;
          break;
        case "cost":
          aVal = a.initialValue;
          bVal = b.initialValue;
          break;
        case "value":
          aVal = a.currentValue;
          bVal = b.currentValue;
          break;
        case "pnl":
          aVal = a.cashPnl;
          bVal = b.cashPnl;
          break;
        default:
          return 0;
      }

      if (aVal < bVal) return sortDesc.value ? 1 : -1;
      if (aVal > bVal) return sortDesc.value ? -1 : 1;
      return 0;
    });
  }

  return groupArray;
});

async function fetchPositions() {
  if (!user.storage.walletAddress) return;

  isLoading.value = true;
  try {
    positions.value = await api.getPositions(user.storage.walletAddress);
  }
  catch (error) {
    console.error("Failed to fetch positions:", error);
  }
  finally {
    isLoading.value = false;
  }
}

function onClickSort(column: string) {
  if (sortBy.value === column) {
    sortDesc.value = !sortDesc.value;
  }
  else {
    sortBy.value = column;
    sortDesc.value = false;
  }
}

function getSortClass(column: string) {
  return {
    "sort-active": sortBy.value === column,
    "cursor-pointer": true
  };
}

function getSortIcon(column: string) {
  if (sortBy.value !== column) return "unfold_more";
  return sortDesc.value ? "keyboard_arrow_down" : "keyboard_arrow_up";
}

function getOutcomeClass(outcome: string) {
  const lower = outcome.toLowerCase();
  if (lower.includes("yes") || lower.includes("true")) return "text-yes";
  if (lower.includes("no") || lower.includes("false")) return "text-no";
  return "";
}

function getPnlClass(pnl: number) {
  if (pnl > 0) return "text-yes";
  if (pnl < 0) return "text-no";
  return "";
}

function onClickClose() {
  isShown.value = false;
}

//Watch for dialog opening to fetch positions
watch(() => isShown.value, (newVal) => {
  if (newVal) {
    fetchPositions();
  }
});
</script>

<style scoped lang="scss">
.dialog-positions {
  .positions-main {
    width: auto;
    height: auto;
    max-width: none !important;
    max-height: 90vh;
    min-width: 900px;
    padding: 0;
  }
}

.positions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.positions-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0;
}

.positions-header-row {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  color: #666;
  position: sticky;
  top: 0;
  z-index: 1;
}

.header-icon {
  width: 50px;
  flex-shrink: 0;
}

.header-market {
  width: 200px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-outcome {
  width: 80px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-price {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
}

.header-shares {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
}

.header-cost {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
}

.header-value {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
}

.header-pnl {
  width: 120px;
  flex-shrink: 0;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
}

.sort-active {
  color: #1976d2;
}

.event-group {
  border-bottom: 1px solid #e0e0e0;
}

.event-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #e0e0e0;
}

.event-icon {
  width: 50px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  img {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    object-fit: cover;
  }
}

.event-title {
  flex: 1;
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-left: 8px;
}

.position-row {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;

  &:hover {
    background-color: #f9f9f9;
  }
}

.row-spacer {
  width: 50px;
  flex-shrink: 0;
}

.row-market {
  width: 200px;
  flex-shrink: 0;
  color: #333;
}

.row-outcome {
  width: 80px;
  flex-shrink: 0;
  font-weight: 600;
}

.row-price {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
}

.row-shares {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
}

.row-cost {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
}

.row-value {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
}

.row-pnl {
  width: 120px;
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
  font-weight: 600;
}

.pnl-percent {
  font-size: 11px;
  opacity: 0.8;
  margin-left: 4px;
}
</style>
