<template>
  <q-dialog v-model:model-value="isShown" class="dialog-positions">
    <q-card class="positions-main">
      <!-- Header -->
      <div class="positions-header">
        <div class="text-h6">Your Positions</div>
        <q-btn icon="close" size="sm" flat @click="onClickClose" />
      </div>

      <!-- Loading state -->
      <div v-if="isLoading" class="q-pa-md text-center">
        <q-spinner size="lg" />
        <div class="q-mt-sm">Loading positions...</div>
      </div>

      <!-- No positions state -->
      <div v-else-if="groupedPositions.length === 0" class="q-pa-md text-center text-grey-6">
        No positions found
      </div>

      <!-- Positions content -->
      <div v-else class="positions-content">
        <!-- Column headers -->
        <div class="positions-header-row">
          <div class="header-icon-space"></div>
          <div class="header-data-section">
            <div class="header-market" @click="onClickSort('title')" :class="getSortClass('title')">
              Market
              <q-icon :name="getSortIcon('title')" size="xs" />
            </div>
            <div class="header-shares" @click="onClickSort('shares')" :class="getSortClass('shares')">
              Shares
            </div>
            <div class="header-outcome" @click="onClickSort('outcome')" :class="getSortClass('outcome')">
              Side
            </div>
            <div class="header-avgprice" @click="onClickSort('avgprice')" :class="getSortClass('avgprice')">
              Price
            </div>
            <div class="header-curprice" @click="onClickSort('price')" :class="getSortClass('price')">
            </div>
            <div class="header-cost" @click="onClickSort('cost')" :class="getSortClass('cost')">
              Cost
            </div>
            <div class="header-value" @click="onClickSort('value')" :class="getSortClass('value')">
              Value
            </div>
            <div class="header-pnl" @click="onClickSort('pnl')" :class="getSortClass('pnl')">
              P&L
            </div>
          </div>
        </div>

        <!-- Event groups -->
        <div v-for="eventGroup in groupedPositions" :key="eventGroup.eventSlug" class="event-group">
          <!-- Event container: flex row with icon and data -->
          <div class="event-container">
            <!-- Event icon (left side) -->
            <div class="event-icon">
              <img :src="eventGroup.icon" :alt="eventGroup.title" />
            </div>

            <!-- Data container: flex column with title and position rows -->
            <div class="data-container">
              <!-- Event title -->
              <div class="event-title">
                {{ eventGroup.title }}
              </div>

              <!-- Position data rows -->
              <div v-for="position in eventGroup.positions" :key="position.asset" class="position-data-row">
                <div class="row-market">{{ getPositionDisplayTitle(position, eventGroup.title) }}</div>
                <div class="row-shares" :class="getOutcomeClass(position.outcome)">{{ formatDecimal(position.size, 0, true) }}</div>
                <div class="row-outcome" :class="getOutcomeClass(position.outcome)">
                  {{ position.outcome }}
                </div>
                <div class="row-avgprice">{{ formatCents(position.avgPrice, 1) }}</div>
                <div class="row-curprice">{{ formatCents(position.curPrice, 1) }}</div>
                <div class="row-cost">{{ formatCurrency(position.initialValue, 0) }}</div>
                <div class="row-value">{{ formatCurrency(position.currentValue, 0) }}</div>
                <div class="row-pnl" :class="getPnlClass(position.cashPnl)">
                  {{ position.cashPnl >= 0 ? '+' : '' }}{{ formatCurrency(position.cashPnl, 0) }}
                  <span class="pnl-percent">({{ position.percentPnl >= 0 ? '+' : '' }}{{ formatDecimal(position.percentPnl, 1, true) }}%)</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { PolyApiPosition, PolyGammaMarket } from "@shared/api-dataclasses-shared";
import { formatCurrency, formatCents, formatDecimal } from "src/utils";
import { useUserStore } from "src/stores/user-store";
import { useApi } from "src/api";

const isShown = defineModel("isShown", { type: Boolean, default: false });

interface EventGroup {
  eventSlug: string;
  title: string;
  icon: string;
  positions: EnhancedPosition[];
}

interface EnhancedPosition extends PolyApiPosition {
  marketTitle?: string;
  eventTitle?: string;
  eventIcon?: string;
}

const user = useUserStore();
const api = useApi();
const isLoading = ref(false);
const positions = ref<PolyApiPosition[]>([]);
const markets = ref<PolyGammaMarket[]>([]);
const sortBy = ref<string>("title");
const sortDesc = ref(false);

const groupedPositions = computed(() => {
  if (!positions.value.length || !markets.value.length) return [];

  //Create enhanced positions with market data
  const enhancedPositions: EnhancedPosition[] = positions.value.map(position => {
    const market = markets.value.find(m => m.conditionId === position.conditionId);
    return {
      ...position,
      marketTitle: market?.groupItemTitle || position.title,
      eventTitle: market?.events?.[0]?.title || position.title,
      eventIcon: market?.events?.[0]?.icon || position.icon
    };
  });

  //Group positions by event
  const groups: Record<string, EventGroup> = {};

  for (const position of enhancedPositions) {
    if (!groups[position.eventSlug]) {
      groups[position.eventSlug] = {
        eventSlug: position.eventSlug,
        title: position.eventTitle || position.title,
        icon: position.eventIcon || position.icon,
        positions: []
      };
    }
    groups[position.eventSlug].positions.push(position);
  }

  //Convert to array and sort each group's positions
  const groupArray = Object.values(groups);

  for (const group of groupArray) {
    group.positions.sort((a, b) => {
      let aVal: any, bVal: any;

      switch (sortBy.value) {
        case "title":
          aVal = (a.marketTitle || a.title).toLowerCase();
          bVal = (b.marketTitle || b.title).toLowerCase();
          break;
        case "outcome":
          aVal = a.outcome.toLowerCase();
          bVal = b.outcome.toLowerCase();
          break;
        case "avgprice":
          aVal = a.avgPrice;
          bVal = b.avgPrice;
          break;
        case "price":
          aVal = a.curPrice;
          bVal = b.curPrice;
          break;
        case "shares":
          aVal = a.size;
          bVal = b.size;
          break;
        case "cost":
          aVal = a.initialValue;
          bVal = b.initialValue;
          break;
        case "value":
          aVal = a.currentValue;
          bVal = b.currentValue;
          break;
        case "pnl":
          aVal = a.cashPnl;
          bVal = b.cashPnl;
          break;
        default:
          return 0;
      }

      if (aVal < bVal) return sortDesc.value ? 1 : -1;
      if (aVal > bVal) return sortDesc.value ? -1 : 1;
      return 0;
    });
  }

  return groupArray;
});

async function fetchPositions() {
  if (!user.storage.walletAddress) return;

  isLoading.value = true;
  try {
    //First fetch positions
    const positionsData = await api.getPositions(user.storage.walletAddress);
    positions.value = positionsData;

    //Then fetch market data for the specific condition IDs from positions
    if (positionsData.length > 0) {
      const conditionIds = positionsData.map(p => p.conditionId);
      const marketsData = await api.getMarkets(undefined, conditionIds);
      markets.value = marketsData;
    }
    else {
      markets.value = [];
    }
  }
  catch (error) {
    console.error("Failed to fetch positions:", error);
  }
  finally {
    isLoading.value = false;
  }
}

function onClickSort(column: string) {
  if (sortBy.value === column) {
    sortDesc.value = !sortDesc.value;
  }
  else {
    sortBy.value = column;
    sortDesc.value = false;
  }
}

function getSortClass(column: string) {
  return {
    "sort-active": sortBy.value === column,
    "cursor-pointer": true
  };
}

function getSortIcon(column: string) {
  if (sortBy.value !== column) return "unfold_more";
  return sortDesc.value ? "keyboard_arrow_down" : "keyboard_arrow_up";
}

function getOutcomeClass(outcome: string) {
  const lower = outcome.toLowerCase();
  if (lower.includes("yes") || lower.includes("true")) return "text-yes bg-yes";
  if (lower.includes("no") || lower.includes("false")) return "text-no bg-no";
  return "";
}

function getPnlClass(pnl: number) {
  if (pnl > 0) return "text-yes";
  if (pnl < 0) return "text-no";
  return "";
}

function getPositionDisplayTitle(position: EnhancedPosition, eventTitle: string) {
  const marketTitle = position.marketTitle || position.title;
  //If market title is the same as event title, don't show it (redundant)
  if (marketTitle === eventTitle) {
    return "";
  }
  return marketTitle;
}

function onClickClose() {
  isShown.value = false;
}

//Watch for dialog opening to fetch positions
watch(() => isShown.value, (newVal) => {
  if (newVal) {
    fetchPositions();
  }
});
</script>

<style scoped lang="scss">
.dialog-positions {
  .positions-main {
    width: auto;
    height: auto;
    max-width: none !important;
    max-height: 90vh;
    min-width: 900px;
    padding: 0;
  }
}

.positions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.positions-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0;
}

.positions-header-row {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  color: #666;
  position: sticky;
  top: 0;
  z-index: 1;
}

.header-icon-space {
  width: 62px;
  flex-shrink: 0;
}

.header-data-section {
  flex: 1;
  display: flex;
  align-items: center;
}

.header-market {
  width: 208px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.header-shares {
  width: 55px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.header-outcome {
  width: 60px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.header-avgprice {
  width: 40px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.header-curprice {
  width: 40px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.header-cost {
  width: 80px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.header-value {
  width: 80px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.header-pnl {
  width: 120px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.sort-active {
  color: #1976d2;
}

.event-group {
  border-bottom: 1px solid #e0e0e0;
}

.event-container {
  display: flex;
  flex-direction: row;
  padding: 0 16px;
  background-color: #fafafa;
}

.event-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-shrink: 0;
  padding-top: 4px;

  img {
    width: 42px;
    height: 42px;
    border-radius: 4px;
    object-fit: cover;
  }
}

.data-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 12px;
}

.event-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  padding: 4px 0 4px 0;
}

.position-data-row {
  display: flex;
  align-items: center;
  padding: 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;

  &:hover {
    background-color: #f9f9f9;
  }

  &:last-child {
    border-bottom: none;
  }
}

.row-market {
  width: 200px;
  flex-shrink: 0;
  color: #333;
  padding-right: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.row-outcome {
  width: 60px;
  flex-shrink: 0;
  font-weight: 600;
  text-align: center;
}

.row-price {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
}

.row-shares {
  width: 55px;
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
  font-weight: 600;
}

.row-cost {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
}

.row-value {
  width: 80px;
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
}

.row-pnl {
  width: 120px;
  flex-shrink: 0;
  text-align: right;
  font-family: monospace;
  font-weight: 600;
}

.pnl-percent {
  font-size: 11px;
  opacity: 0.8;
  margin-left: 4px;
}
</style>
