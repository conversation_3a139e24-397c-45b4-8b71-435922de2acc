import { ApiKeyCreds, AssetType, BalanceAllowanceResponse, ClobClient, OrderType, PriceHistoryInterval, Side, TickSize, Trade } from '@polymarket/clob-client';
import { Wallet } from 'ethers';
import { PolyApiPosition, PolyCancelOrderResponse, PolyDataTrade, PolyElonTweetResponse, PolyGammaComment, PolyGammaEvent, PolyGammaHistoryItem, PolyClobOpenOrder, PolyPostCommentResponse, PolyPostOrderResponse, PolyPostReactionResponse, PolyPriceHistory, PolyPriceHistoryItem, PolyReactionType, PolyUserProfileResponse, PolyWSTrade, PolySearchResponse, PolySearchEventStatus, PolySearchEventSort, PolySearchEventFrequency, PolyPositionSort, PolyGammaMarket } from '@shared/api-dataclasses-shared';
import axios, { AxiosInstance } from 'axios';

const clobApiUrl = "https://clob.polymarket.com";
const gammaApiUrl = "https://gamma-api.polymarket.com";
const dataApiUrl = "https://data-api.polymarket.com";
const polyApiUrl = "https://polymarket.com/api"
const polyLbApiUrl = "https://lb-api.polymarket.com";

const gammaPostHeaders = {
  'accept': 'application/json, text/plain, */*',
  'accept-language': 'en-US,en;q=0.9',
  'content-type': 'application/json',
  'priority': 'u=1, i',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Brave";v="133", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-site',
  'sec-gpc': '1',
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
  'Referrer-Policy': 'no-referrer'
};

//Poly axios instance (handle array params properly eg param=val1&param=val2 instead of param=val1,val2)
const polyAxios: AxiosInstance = axios.create({
  paramsSerializer: (params) => {
    const searchParams = new URLSearchParams();
    for (const key in params) {
      const value = params[key];
      if (Array.isArray(value)) {
        for (const v of value) {
          searchParams.append(key, v);
        }
      }
      else if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    }
    return searchParams.toString();
  }
});

// Use Web3Provider to get the user's wallet signer
//const provider = new providers.Web3Provider(window.ethereum);
//const signer = provider.getSigner(); // JsonRpcSigner, supports _signTypedData

export class PolyClient {
  private clobClient: ClobClient;
  private creds: ApiKeyCreds;
  private lastElonTweetTime: Date;
  private lastElonTweetCount: number;
  private polySessionCookie: string | undefined;

  constructor(privateKey: string, polyKey: string, secret: string, passphrase: string)
  {
    this.creds = { key: polyKey, secret: secret, passphrase: passphrase };
    const signer = new Wallet(privateKey);

    //137 is main (non-test) chain ID
    //0 = EOA (direct trading from wallet)
    //1 = POLY_PROXY (Email/Magic account with proxy)
    //2 = POLY_GNOSIS_SAFE (Browser wallet with proxy - MetaMask/Brave)
    const sigType = config.polyClobSignatureType;
    this.clobClient = new ClobClient(clobApiUrl, 137, signer, this.creds, sigType, sigType == 0 ? config.polyBaseWallet : config.polyProxyWallet);
    this.lastElonTweetTime = new Date()
    this.lastElonTweetCount = 0;
  }

  public setPolySessionCookie(cookie: string)
  {
    this.polySessionCookie = cookie;
  }

  public async getEvent(slug: string): Promise<PolyGammaEvent | null>
  {
    const res = await polyAxios.get(gammaApiUrl + "/events", { params: { slug }});

    if (res.data.length > 0)
      return res.data[0];

    return null;
  }

  public async getMarkets(ids?: string[], conditionIds?: string[]): Promise<PolyGammaMarket[]>
  {
    if (!ids && !conditionIds) {
      throw new Error("Must specify either ids or conditionIds");
    }

    const params: any = {
      ids: ids,
      condition_ids: conditionIds
    };

    console.log(params);

    const res = await polyAxios.get(gammaApiUrl + "/markets", { params });
    return res.data;
  }

  public async getPositions(userBaseAddress: string, marketCondIds?: string[], sortBy?: PolyPositionSort, sortIsDesc?: boolean, searchTitle?: string): Promise<PolyApiPosition[]>
  {
    const params: any = {
      user: userBaseAddress,
      sizeThreshold: 0.1,
      limit: 50,
      offset: 0,
      sortBy,
      sortDirection: sortIsDesc ? "DESC" : "ASC",
      title: searchTitle,
      market: (marketCondIds && marketCondIds.length > 0) ? marketCondIds?.join(",") : undefined
    };

    const res = await polyAxios.get(dataApiUrl + "/positions", { params });

    return res.data;
  }

  public async getOwnTradeHistory(market: string): Promise<Trade[]>
  {
    return this.clobClient.getTrades({ market });
  }

  public async placeOrder(assetId: string, price: number, shares: number, isBuy: boolean, tickSize?: TickSize): Promise<PolyPostOrderResponse>
  {
    const order = await this.clobClient.createOrder({
      tokenID: assetId,
      price: price,
      size: shares,
      side: isBuy ? Side.BUY : Side.SELL
    },
    {
      tickSize: tickSize
    });

    //BUGFIX: clobclient caches ticksizes that don't update on change and prevent orders from being placed
    (this.clobClient as any).tickSizes = {};
    return await this.clobClient.postOrder(order, OrderType.GTC);
  }

  public async getOrders(): Promise<PolyClobOpenOrder[]>
  {
    const res = await this.clobClient.getOpenOrders() as PolyClobOpenOrder[];
    return res;
  }

  public async cancelOrder(orderId: string): Promise<PolyCancelOrderResponse>
  {
    return await this.clobClient.cancelOrder({ orderID: orderId });
  }

  public async cancelOrders(orderIds: string[]): Promise<PolyCancelOrderResponse>
  {
    return await this.clobClient.cancelOrders(orderIds);
  }

  public async getBook(assetIdA: string, assetIdB: string): Promise<Record<string, any>[]>
  {
    return this.clobClient.getOrderBooks([{token_id: assetIdA, side: Side.BUY}, {token_id: assetIdA, side: Side.SELL}, {token_id: assetIdB, side: Side.BUY}, {token_id: assetIdB, side: Side.SELL}]);
  }

  public async getBalance(): Promise<number>
  {
    const res = await this.clobClient.getBalanceAllowance({ asset_type: AssetType.COLLATERAL });
    return Number(res.balance) / 1000000;
    //this.getUserProfile(global.config.polyBaseWallet);
  }

  public async getPriceHistory(assetId: string, interval: PriceHistoryInterval): Promise<PolyPriceHistory>
  {
    const fidelity = interval === "1d" ? 5 : ((interval === "1h" || interval == "6h") ? 1 : 30);
    const res = await this.clobClient.getPricesHistory({ market: assetId, fidelity, interval })

    return res as unknown as PolyPriceHistory; //Dunno why tf clob client returns an array of MarketPrice when it's not that
  }

  public async getElonTweets(eventId: string): Promise<PolyElonTweetResponse>
  {
    const res: { data: PolyElonTweetResponse } = await polyAxios.get(`${gammaApiUrl}/events/${eventId}/tweet-count`);

    return res.data;
  }

  public async getComments(parentId: string, parentType: "Event" | "Series", offset: number, limit: number): Promise<PolyGammaComment[]>
  {
    const res = await polyAxios.get(`${gammaApiUrl}/comments`, {
      params: {
        parent_entity_id: parentId,
        offset,
        limit,
        order: "createdAt", ascending: false, parent_entity_type: parentType, get_positions: true, get_reports: true,
      }
    });
    return res.data;
  }

  public async postComment(text: string, parentId: number, parentType: "Event" | "Series", parentCommentId?: string, replyAddress?: string): Promise<PolyPostCommentResponse>
  {
    this.checkPolyCookie();

    const res = await polyAxios.post(`${gammaApiUrl}/comments`, {
      body: text,
      parentEntityId: parentId,
      parentEntityType: parentType,
      parentCommentId: parentCommentId,
      replyAddress: replyAddress
    }, {
      headers: {
        ...gammaPostHeaders,
        'cookie': this.polySessionCookie
      }
    });

    return res.data;
  }

  public async deleteComment(commentId: number): Promise<PolyPostCommentResponse>
  {
    this.checkPolyCookie();

    const res = await polyAxios.delete(`${gammaApiUrl}/comments/${commentId}`, {
      headers: {
        ...gammaPostHeaders,
        'cookie': this.polySessionCookie
      }
    });

    return res.data;
  }

  public async postReaction(commentId: number, reactionType: PolyReactionType): Promise<PolyPostReactionResponse>
  {
    this.checkPolyCookie();

    const res = await polyAxios.post(`${gammaApiUrl}/reactions`, {
      commentID: commentId,
      reactionType: reactionType
      }, {
      headers: {
        ...gammaPostHeaders,
        'cookie': this.polySessionCookie
      }
    });

    return res.data;
  }

  public async deleteReaction(reactionId: number): Promise<void> {
    this.checkPolyCookie();

    const res = await polyAxios.delete(`${gammaApiUrl}/reactions/${reactionId}`, {
      headers: {
        ...gammaPostHeaders,
        'cookie': this.polySessionCookie
      }
    });

    return res.data;
  }

  public async getUserProfile(userAddress: string): Promise<PolyUserProfileResponse> {
    const res = await polyAxios.get(`${polyApiUrl}/profile/userData`, { params: { address: userAddress }});
    return res.data;
  }

  public async getUserActivity(proxyWallet: string, marketIds: string[] | null = null, offset: number = 0, limit: number = 30): Promise<PolyGammaHistoryItem[]> {
    const params: any = { user: proxyWallet, offset, limit };
    if (marketIds) {
      params.market = marketIds.join(",");
    }
    const res = await polyAxios.get(`${dataApiUrl}/activity`, { params });
    return res.data;
  }

  public async getUserProfit(proxyWallet: string): Promise<number> {
    const params: any = { window: "all", limit: 1, address: proxyWallet };
    const res = await polyAxios.get(`${polyLbApiUrl}/profit`, { params });
    /*{
      "proxyWallet": "******************************************",
      "amount": 1470.56726208778,
      "pseudonym": "Arctic-Grasp",
      "name": "mk27",
      "bio": "",
      "profileImage": "",
      "profileImageOptimized": ""
    }*/
    return Number(res.data[0]?.amount ?? 0);
  }

  public async getMarketActivity(marketIds: string[] | string, offset: number = 0, limit: number = 30, filterAmount: number = 0.1): Promise<PolyDataTrade[]> {
    const marketStr = typeof marketIds === "string" ? marketIds : marketIds.join(",");
    const params: any = { market: marketStr, offset, limit, filterType: "CASH", filterAmount };
    const res = await polyAxios.get(`${dataApiUrl}/trades`, { params });
    return res.data;
  }

  public async quickSearchEvents(query: string, eventsStatus: PolySearchEventStatus): Promise<PolySearchResponse> {
    if (eventsStatus != "active" && eventsStatus != "resolved" && eventsStatus != "all") {
      throw new Error("Invalid events_status, must be active, resolved, or all");
    }
    const params = { q: query, events_status: eventsStatus };
    const res = await polyAxios.get(`${polyApiUrl}/events/global`, { params });
    //res.data will be [] if no matches for some dumb reason so fill in a blank response
    if (res.data.length === 0) {
      res.data = { events: [], tags: [], hasMore: false };
    }
    return res.data;
  }

  public async searchEvents(
    query: string,
    page: number = 1,
    eventsStatus: PolySearchEventStatus = "active",
    sort?: PolySearchEventSort,
    frequency?: PolySearchEventFrequency,
    category?: string,
    hideSports?: boolean
  ): Promise<PolySearchResponse> {
    if (eventsStatus != "active" && eventsStatus != "resolved" && eventsStatus != "all") {
      throw new Error("Invalid events_status, must be active, resolved, or all");
    }
    /*
      _p is page
      _q is search query
      _sts is status (active, resolved, or all)
      _s is sort, format option:desc/asc (options are volume_24hr "trending" on ui, competitive, start_date "newest" on ui, liquidity, volume, end_date "ending soon" on ui,
      _c is category (all, ai, trade-war, etc; labeled "topics" on ui and there are many)
      _r is frequency (daily, weekly, monthly)
      NOTE: _r does not appear to be respected at all
    */
    const params = { _q: query, _p: page, _sts: eventsStatus, _s: sort, _c: category, _r: frequency === "all" ? undefined : frequency, _hide_sports: hideSports };
    const res = await polyAxios.get(`${polyApiUrl}/events/search`, { params });
    return res.data;
  }

  private checkPolyCookie() {
    if (!this.polySessionCookie) {
      throw new Error("Polymarket session cookie not set");
    }
  }
}
