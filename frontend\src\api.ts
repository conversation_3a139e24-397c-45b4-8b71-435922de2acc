import { ApiHistoryItem, ApiMarketActivityItem, PolyApiPosition, PolyCancelOrderResponse, PolyDataMarket, PolyElonTweetResponse, PolyGammaComment, PolyGammaCommentProfile, PolyGammaEvent, PolyGammaHistoryItem, PolyClobOpenOrder, PolyPostCommentResponse, PolyPostOrderResponse, PolyPostReactionResponse, PolyPriceHistory, PolyPriceHistoryItem, PolyReactionType, PolyUserBalanceResponse, PolyUserProfileResponse, PolyWSBook, PriceHistoryInterval, PolySearchResponse, PolySearchEventStatus, PolySearchEventSort, PolySearchEventFrequency, PolyPositionSort, PolyGammaMarket } from '@shared/api-dataclasses-shared';
import axios, { AxiosError, AxiosInstance } from 'axios';
import { Loading } from 'quasar';
import { useNotifier } from './notifier';

declare module 'axios' {
  export interface AxiosRequestConfig {
    noLoading?: boolean;
  }
}

const polyDataApiUrl = "https://data-api.polymarket.com";
//Api singleton
let apiInstance: AppApi | null = null;
//Pending requests (for loading indicator)
let pendingCount = 0;
const notifier = useNotifier();

/**
 * The api singleton `$api` meant to interact with the backend.
 * @returns the api instance
 */
export function useApi(): AppApi {
  if (!apiInstance) {
    apiInstance = new AppApi();
  }

  return apiInstance;
}

class AppApi {
  public axiosInstance: AxiosInstance;
  private shouldLoad: boolean = false;

  public constructor() {
    this.axiosInstance = axios.create({
      baseURL: process.env.NODE_ENV === 'production' ? '' : "http://" + process.env.API_URL,
      withCredentials: true, //allow cookies
    });

    //Intercept requests
    this.axiosInstance.interceptors.request.use(
      (config) => {
        config.noLoading = config.noLoading ?? !this.shouldLoad;
        if (!config.noLoading) {
          Loading.show();
          pendingCount++;
        }
        return config;
      },
      //This should only be called if the above interceptor function throws an error, basically a try/catch
      (error) => {
        return Promise.reject(error);
      }
    );

    //Intercept responses (global error handling, etc)
    this.axiosInstance.interceptors.response.use(
      //Handles response codes within 200-299 range
      (response) => {
        Loading.hide();

        //Hide loading indicator
        if (response.request && !response.request.noLoading) {
          pendingCount--;
          if (pendingCount === 0) {
            Loading.hide();
          }
        }

        return response;
      },
      //Handles response codes >=300 and no response scenarios
      (error: AxiosError) => {
        if (error.status! == 302 || error.status! == 307) {
          alert("REDIRECT RECIEVED");
          console.log(error);
        }
        //Response received with error code
        if (error.response) {
          const data: any = error.response.data;
          console.error('Error response', error.response.status, data);
          notifier.error(`HTTP Error ${error.response.status}: ${data?.error ? data.error : JSON.stringify(data)}`);
        }
        //No response received
        else if (error.request) {
          console.error('No response received for the request', error.request);
        }
        //Runtime error setting up request
        else {
          console.error('Error setting up the request', error.message);
        }

        //Hide loading indicator
        if (error.request && !error.request.noLoading) {
          pendingCount--;
          if (pendingCount === 0) {
            Loading.hide();
          }
        }

        return Promise.reject(error);
      }
    );
  }

  public enableLoading(shouldLoad: boolean): void {
    this.shouldLoad = shouldLoad;
  }

  public async getEvent(slug: string): Promise<PolyGammaEvent> {
    const res = await this.axiosInstance.get('/events', { params: { slug } });
    return res.data;
  }

  public async getMarkets(ids?: string[], condIds?: string[]): Promise<PolyGammaMarket[]> {
    if (!ids && !condIds) {
      throw new Error("Must specify either ids or conditionIds");
    }

    const params = {
      condIds: condIds?.join(","),
      ids: ids?.join(",")
    };

    const res = await this.axiosInstance.get('/events/markets', { params });
    return res.data;
  }

  public async getPositions(userWallet: string, marketCondIds?: string[], sortBy?: PolyPositionSort, sortIsDesc?: boolean, searchTitle?: string): Promise<PolyApiPosition[]> {

    let params: any = {
      user: userWallet,
      sizeThreshold: 0.1,
      limit: 50,
      offset: 0,
      sortBy,
      sortDirection: sortIsDesc ? "DESC" : "ASC",
      title: searchTitle,
      market: (marketCondIds && marketCondIds.length > 0) ? marketCondIds?.join(",") : undefined
    };

    const res = await axios.get(polyDataApiUrl + "/positions", { params });
    return res.data;
  }

  //Note: If you ever want to move this clientside, must install clob-client, ethers, polyfills for Node stuff like Buffer, and magic.link setup (with api key)
  public async placeOrder(assetId: string, price: number, shares: number, isBuy: boolean, tickSize?: string): Promise<PolyPostOrderResponse> {
    const res = await this.axiosInstance.post('/polyuser/orders', {
      assetId,
      price,
      shares,
      isBuy,
      tick: tickSize,
    });
    return res.data;
  }

  public async getOrders(): Promise<PolyClobOpenOrder[]> {
    const res = await this.axiosInstance.get('/polyuser/orders');
    return res.data;
  }

  public async cancelOrder(orderId: string): Promise<PolyCancelOrderResponse> {
    const res = await this.axiosInstance.delete('/polyuser/orders', { data: { orderId } });
    return res.data;
  }

  public async cancelOrders(orderIds: string[]): Promise<PolyCancelOrderResponse> {
    const res = await this.axiosInstance.delete('/polyuser/orders', { data: { orderId: orderIds.join(',') } });
    return res.data;
  }

  public async getOrderbook(market: PolyDataMarket): Promise<PolyWSBook[]> {
    const res = await this.axiosInstance.get('/events/market/orderbook', { params: { idA: market.assetIdA, idB: market.assetIdB } });
    return res.data;
  }

  public async getUserBalance(): Promise<PolyUserBalanceResponse> {
    const res = await this.axiosInstance.get('/polyuser/balance');
    return res.data;
  }

  public async getUserProfile(userAddress: string): Promise<PolyUserProfileResponse> {
    const res = await this.axiosInstance.get('/polyuser/profile', { params: { userAddress } });
    return res.data;
  }

  public async getUserProfit(proxyWallet: string): Promise<number> {
    const res = await this.axiosInstance.get('/polyuser/profit', { params: { proxyWallet: proxyWallet } });
    return res.data.profit;
  }

  public async getPriceHistory(assetId: string, interval: PriceHistoryInterval): Promise<PolyPriceHistory> {
    const res = await this.axiosInstance.get('/events/market/price-history', { params: { assetId, interval } });
    return res.data;
  }

  public async getElonTweetCount(eventId: string): Promise<PolyElonTweetResponse> {
    const res = await this.axiosInstance.get('/events/elon-tweets', { params: { eventId } });
    return res.data;
  }

  public async getComments(parentId: string, parentType: string, offset: number, limit: number): Promise<PolyGammaComment[]> {
    const res = await this.axiosInstance.get('/comments', { params: { parentId, parentType, offset, limit } });
    return res.data;
  }

  //NOTE: Abandoned this because it only works for non-series events (eg not elon tweet)
  // public async getCommentCount(parentId: string, parentType: string): Promise<number> {
  //   const res = await this.axiosInstance.get('/comments/count', { params: { parentId, parentType } });
  //   return res.data;
  // }

  public async postComment(polyCookie: string, text: string, parentId: string, parentType: string, parentCommentId?: string, replyAddress?: string): Promise<PolyPostCommentResponse> {
    const res = await this.axiosInstance.post('/comments', { polyCookie, text, parentId, parentType, parentCommentId, replyAddress });
    return res.data;
  }

  public async postReaction(polyCookie: string, reactToCommentId: string, reactionType: PolyReactionType): Promise<PolyPostReactionResponse> {
    const res = await this.axiosInstance.post('/comments/reaction', { polyCookie, commentId: reactToCommentId, reactionType });
    return res.data;
  }

  public async deleteReaction(polyCookie: string, reactionId: string): Promise<PolyPostReactionResponse> {
    const res = await this.axiosInstance.post('/comments/reaction-delete', { polyCookie, reactionId });
    return res.data;
  }

  public async deleteComment(polyCookie: string, commentId: string): Promise<void> {
    await this.axiosInstance.post('/comments/delete', { polyCookie, commentId });
  }

  public async getFullUserHistory(userProxyWallet: string, marketCondIds: string[]): Promise<ApiHistoryItem[]> {
    const res = await this.axiosInstance.post('/polyuser/history', { proxyWallet: userProxyWallet, markets: marketCondIds.join(",") });
    return res.data;
  }

  public async getRecentMarketHistory(marketCondIds: string[], timeIntervalMs: number = 3600000): Promise<ApiMarketActivityItem[]> {
    timeIntervalMs = Math.round(timeIntervalMs);
    const res = await this.axiosInstance.get('/events/market/history', { params: { market: marketCondIds.join(","), interval: timeIntervalMs } });
    return res.data;
  }

  public async getPrivateKeys(): Promise<Record<string, string>> {
    const res = await this.axiosInstance.get('/pks');
    return res.data;
  }

  public async quickSearchEvents(query?: string, eventsStatus: PolySearchEventStatus = "active"): Promise<PolySearchResponse> {
    const params: any = { q: query, status: eventsStatus };
    const res = await this.axiosInstance.get('/events/quick-search', { params });
    return res.data;
  }

  public async searchEvents(query?: string, page: number = 1, eventsStatus: PolySearchEventStatus = "active", sort?: PolySearchEventSort, frequency?: PolySearchEventFrequency, category?: string, hideSports?: boolean): Promise<PolySearchResponse> {
    const params: any = { q: query, status: eventsStatus, page, sort, freq: frequency, cat: category, noSports: hideSports };
    const res = await this.axiosInstance.get('/events/search', { params });
    return res.data;
  }
}
